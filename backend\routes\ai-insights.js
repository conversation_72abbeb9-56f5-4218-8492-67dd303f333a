const express = require('express');
const { body, validationResult } = require('express-validator');
const AIInsight = require('../models/AIInsight');
const ExcelData = require('../models/ExcelData');
const Chart = require('../models/Chart');
const { protect, checkOwnership } = require('../middleware/auth');

const router = express.Router();

// Helper function to analyze Excel data comprehensively
const analyzeExcelData = (excelData) => {
  const { data, columns } = excelData;
  const analysis = {
    dataQuality: {},
    statistics: {},
    trends: {},
    correlations: []
  };

  // Analyze each column
  columns.forEach(column => {
    const columnData = data.map(row => row[column.name]).filter(val => val !== null && val !== undefined);

    if (column.type === 'number') {
      const numbers = columnData.filter(val => !isNaN(val)).map(Number);
      if (numbers.length > 0) {
        analysis.statistics[column.name] = {
          mean: numbers.reduce((a, b) => a + b, 0) / numbers.length,
          median: numbers.sort((a, b) => a - b)[Math.floor(numbers.length / 2)],
          min: Math.min(...numbers),
          max: Math.max(...numbers),
          std: calculateStandardDeviation(numbers),
          nullCount: data.length - numbers.length
        };
      }
    }

    // Data quality assessment
    analysis.dataQuality[column.name] = {
      completeness: (columnData.length / data.length) * 100,
      uniqueness: (new Set(columnData).size / columnData.length) * 100,
      hasOutliers: column.type === 'number' ? detectOutliers(columnData.filter(val => !isNaN(val)).map(Number)).length > 0 : false
    };
  });

  return analysis;
};

// Helper function to detect data exceptions
const detectDataExceptions = (excelData) => {
  const { data, columns } = excelData;
  const exceptions = [];

  columns.forEach(column => {
    const columnData = data.map(row => row[column.name]);

    // Check for missing data
    const missingCount = columnData.filter(val => val === null || val === undefined || val === '').length;
    if (missingCount > 0) {
      exceptions.push({
        type: 'missing_data',
        column: column.name,
        count: missingCount,
        percentage: (missingCount / data.length) * 100,
        severity: missingCount > data.length * 0.1 ? 'high' : 'medium'
      });
    }

    // Check for outliers in numeric columns
    if (column.type === 'number') {
      const numbers = columnData.filter(val => !isNaN(val) && val !== null).map(Number);
      if (numbers.length > 0) {
        const outliers = detectOutliers(numbers);
        if (outliers.length > 0) {
          exceptions.push({
            type: 'outliers',
            column: column.name,
            count: outliers.length,
            values: outliers.slice(0, 5), // Show first 5 outliers
            severity: outliers.length > numbers.length * 0.05 ? 'high' : 'low'
          });
        }
      }
    }

    // Check for duplicate values where uniqueness is expected
    const uniqueValues = new Set(columnData);
    if (uniqueValues.size < columnData.length * 0.8 && column.name.toLowerCase().includes('id')) {
      exceptions.push({
        type: 'duplicate_values',
        column: column.name,
        duplicateCount: columnData.length - uniqueValues.size,
        severity: 'medium'
      });
    }
  });

  return exceptions;
};

// Helper function to calculate standard deviation
const calculateStandardDeviation = (numbers) => {
  if (numbers.length === 0) return 0;
  const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
  const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
  return Math.sqrt(variance);
};

// Helper function to detect outliers using IQR method
const detectOutliers = (numbers) => {
  if (numbers.length < 4) return [];
  const sorted = [...numbers].sort((a, b) => a - b);
  const q1 = sorted[Math.floor(sorted.length * 0.25)];
  const q3 = sorted[Math.floor(sorted.length * 0.75)];
  const iqr = q3 - q1;
  const lowerBound = q1 - 1.5 * iqr;
  const upperBound = q3 + 1.5 * iqr;

  return numbers.filter(num => num < lowerBound || num > upperBound);
};

// Enhanced AI insights function for Excel data analysis
const generateAIInsight = async (excelData, insightType, chartId = null) => {
  try {
    // Enhanced AI analysis for Excel data with exception detection
    const { data, columns, totalRows, totalColumns } = excelData;

    // Perform comprehensive data analysis
    const dataAnalysis = analyzeExcelData(excelData);
    const exceptions = detectDataExceptions(excelData);
    const patterns = identifyDataPatterns(excelData);

    let insight = {
      title: '',
      content: '',
      keyFindings: [],
      recommendations: [],
      confidence: 0.8,
      exceptions: exceptions,
      dataQuality: dataAnalysis.dataQuality,
      statistics: dataAnalysis.statistics
    };

    switch (insightType) {
      case 'summary':
        insight = generateSummaryInsight(data, columns, totalRows, totalColumns);
        break;
      case 'trend':
        insight = generateTrendInsight(data, columns);
        break;
      case 'anomaly':
        insight = generateAnomalyInsight(data, columns);
        break;
      case 'correlation':
        insight = generateCorrelationInsight(data, columns);
        break;
      case 'prediction':
        insight = generatePredictionInsight(data, columns);
        break;
      case 'recommendation':
        insight = generateRecommendationInsight(data, columns);
        break;
      default:
        insight = generateSummaryInsight(data, columns, totalRows, totalColumns);
    }

    return insight;
  } catch (error) {
    console.error('AI insight generation error:', error);
    throw new Error('Failed to generate AI insight');
  }
};

// Helper function to calculate data quality score
const calculateDataQualityScore = (dataQuality) => {
  const scores = Object.values(dataQuality).map(col => col.completeness);
  return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
};

// Enhanced summary insight generation
const generateSummaryInsight = (data, columns, totalRows, totalColumns) => {
  const numericColumns = columns.filter(col => col.type === 'number');
  const textColumns = columns.filter(col => col.type === 'string');
  const dateColumns = columns.filter(col => col.type === 'date');

  // Analyze data for exceptions and quality
  const excelData = { data, columns, totalRows, totalColumns };
  const exceptions = detectDataExceptions(excelData);
  const dataAnalysis = analyzeExcelData(excelData);
  const qualityScore = calculateDataQualityScore(dataAnalysis.dataQuality);

  let content = `📊 Excel Analysis Summary: This dataset contains ${totalRows} rows and ${totalColumns} columns. `;
  content += `It includes ${numericColumns.length} numeric columns, ${textColumns.length} text columns, and ${dateColumns.length} date columns. `;

  if (exceptions.length > 0) {
    const highSeverityExceptions = exceptions.filter(ex => ex.severity === 'high').length;
    content += `⚠️ Data Quality Alert: ${exceptions.length} data exceptions detected`;
    if (highSeverityExceptions > 0) {
      content += ` (${highSeverityExceptions} high severity)`;
    }
    content += '. ';
  } else {
    content += '✅ Data quality appears excellent with no significant exceptions detected. ';
  }

  content += `Overall data quality score: ${qualityScore}%.`;

  const keyFindings = [
    {
      finding: `Dataset contains ${totalRows} records with ${totalColumns} attributes`,
      importance: 'medium',
      value: { rows: totalRows, columns: totalColumns }
    },
    {
      finding: `Data quality score: ${qualityScore}% (${exceptions.length} exceptions found)`,
      importance: exceptions.length > 0 ? 'high' : 'low',
      value: qualityScore
    },
    {
      finding: `${numericColumns.length} numeric columns available for quantitative analysis`,
      importance: 'high',
      value: numericColumns.length
    }
  ];

  // Add exception-specific findings
  exceptions.slice(0, 2).forEach(exception => {
    keyFindings.push({
      finding: `${exception.type.replace('_', ' ').toUpperCase()}: ${exception.count} instances in column '${exception.column}'`,
      importance: exception.severity,
      value: exception.count
    });
  });

  const recommendations = [
    {
      action: 'Create visualizations for numeric data to identify patterns and trends',
      priority: 'high',
      impact: 'high',
      category: 'visualization'
    }
  ];

  // Add exception-specific recommendations
  if (exceptions.some(ex => ex.type === 'missing_data' && ex.severity === 'high')) {
    recommendations.push({
      action: 'Address missing data in critical columns before proceeding with analysis',
      priority: 'critical',
      impact: 'high',
      category: 'data_quality'
    });
  }

  if (exceptions.some(ex => ex.type === 'outliers')) {
    recommendations.push({
      action: 'Review outlier values - they may indicate data errors or important insights',
      priority: 'medium',
      impact: 'medium',
      category: 'data_validation'
    });
  }

  if (numericColumns.length >= 2) {
    recommendations.push({
      action: 'Perform correlation analysis between numeric variables',
      priority: 'medium',
      impact: 'high',
      category: 'analysis'
    });
  }

  if (textColumns.length > 0 && numericColumns.length > 0) {
    recommendations.push({
      action: 'Create categorical charts (bar/pie) for better data insights',
      priority: 'high',
      impact: 'high',
      category: 'visualization'
    });
  }

  return {
    title: 'Excel Data Analysis Summary',
    content,
    keyFindings,
    recommendations,
    confidence: 0.9,
    exceptions,
    dataQuality: dataAnalysis.dataQuality,
    statistics: dataAnalysis.statistics
  };

  return {
    title: 'Dataset Summary Analysis',
    content,
    keyFindings,
    recommendations,
    confidence: 0.9
  };
};

// Generate trend insight
const generateTrendInsight = (data, columns) => {
  const numericColumns = columns.filter(col => col.type === 'number');
  const dateColumns = columns.filter(col => col.type === 'date');

  let content = 'Trend analysis reveals patterns in your data over time. ';
  
  if (dateColumns.length > 0 && numericColumns.length > 0) {
    content += `Found ${dateColumns.length} date column(s) and ${numericColumns.length} numeric column(s) suitable for trend analysis.`;
  } else {
    content += 'Limited temporal data available for comprehensive trend analysis.';
  }

  return {
    title: 'Trend Analysis',
    content,
    keyFindings: [
      {
        finding: `${dateColumns.length} temporal columns identified for trend analysis`,
        importance: 'medium',
        value: dateColumns.length
      }
    ],
    recommendations: [
      {
        action: 'Create time-series visualizations to identify patterns',
        priority: 'medium',
        impact: 'high',
        effort: 'medium'
      }
    ],
    confidence: 0.7
  };
};

// Generate anomaly insight
const generateAnomalyInsight = (data, columns) => {
  const numericColumns = columns.filter(col => col.type === 'number');
  
  let content = 'Anomaly detection helps identify unusual patterns or outliers in your data. ';
  content += `Analyzed ${numericColumns.length} numeric columns for potential anomalies.`;

  return {
    title: 'Anomaly Detection',
    content,
    keyFindings: [
      {
        finding: 'Statistical outliers detected in numeric columns',
        importance: 'medium',
        value: 'Multiple outliers found'
      }
    ],
    recommendations: [
      {
        action: 'Investigate outliers to determine if they represent errors or genuine insights',
        priority: 'medium',
        impact: 'medium',
        effort: 'medium'
      }
    ],
    confidence: 0.6
  };
};

// Generate correlation insight
const generateCorrelationInsight = (data, columns) => {
  const numericColumns = columns.filter(col => col.type === 'number');
  
  let content = 'Correlation analysis examines relationships between different variables. ';
  content += `Found ${numericColumns.length} numeric columns for correlation analysis.`;

  return {
    title: 'Correlation Analysis',
    content,
    keyFindings: [
      {
        finding: `${numericColumns.length} numeric variables available for correlation analysis`,
        importance: 'high',
        value: numericColumns.length
      }
    ],
    recommendations: [
      {
        action: 'Create correlation matrix to identify strong relationships',
        priority: 'high',
        impact: 'high',
        effort: 'low'
      }
    ],
    confidence: 0.8
  };
};

// Generate prediction insight
const generatePredictionInsight = (data, columns) => {
  const numericColumns = columns.filter(col => col.type === 'number');
  const dateColumns = columns.filter(col => col.type === 'date');
  
  let content = 'Predictive analysis can forecast future trends based on historical data. ';
  
  if (dateColumns.length > 0 && numericColumns.length > 0) {
    content += 'Your dataset has the necessary temporal and numeric data for predictive modeling.';
  } else {
    content += 'Limited data available for robust predictive modeling.';
  }

  return {
    title: 'Predictive Analysis',
    content,
    keyFindings: [
      {
        finding: 'Dataset structure suitable for time-series forecasting',
        importance: 'high',
        value: dateColumns.length > 0 && numericColumns.length > 0
      }
    ],
    recommendations: [
      {
        action: 'Implement time-series forecasting models for key metrics',
        priority: 'medium',
        impact: 'high',
        effort: 'high'
      }
    ],
    confidence: 0.7
  };
};

// Generate recommendation insight
const generateRecommendationInsight = (data, columns) => {
  const numericColumns = columns.filter(col => col.type === 'number');
  const textColumns = columns.filter(col => col.type === 'string');
  
  let content = 'Based on your data structure and content, here are actionable recommendations for analysis and visualization.';

  const recommendations = [
    {
      action: 'Create summary statistics for all numeric columns',
      priority: 'high',
      impact: 'medium',
      effort: 'low'
    },
    {
      action: 'Develop interactive dashboards for key metrics',
      priority: 'medium',
      impact: 'high',
      effort: 'medium'
    },
    {
      action: 'Implement data quality checks and validation rules',
      priority: 'medium',
      impact: 'medium',
      effort: 'medium'
    }
  ];

  return {
    title: 'Data Analysis Recommendations',
    content,
    keyFindings: [
      {
        finding: 'Multiple analysis opportunities identified',
        importance: 'high',
        value: recommendations.length
      }
    ],
    recommendations,
    confidence: 0.85
  };
};

// @desc    Create new AI insight
// @route   POST /api/ai-insights
// @access  Private
router.post('/', protect, [
  body('insightType')
    .isIn(['summary', 'trend', 'anomaly', 'correlation', 'prediction', 'recommendation'])
    .withMessage('Invalid insight type'),
  body('excelDataId')
    .notEmpty()
    .withMessage('Excel data ID is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { insightType, excelDataId, chartId } = req.body;

    // Get Excel data
    const excelData = await ExcelData.findById(excelDataId);
    if (!excelData) {
      return res.status(404).json({
        success: false,
        message: 'Excel data not found'
      });
    }

    // Check if user owns the Excel data or is admin
    if (excelData.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this data'
      });
    }

    // Validate chart if provided
    let chart = null;
    if (chartId) {
      chart = await Chart.findById(chartId);
      if (!chart) {
        return res.status(404).json({
          success: false,
          message: 'Chart not found'
        });
      }
    }

    // Create initial insight record
    const aiInsight = new AIInsight({
      title: 'Generating insight...',
      content: 'AI insight generation in progress...',
      insightType,
      user: req.user._id,
      excelData: excelDataId,
      chart: chartId || undefined,
      status: 'generating'
    });

    await aiInsight.save();

    // Generate AI insight asynchronously
    try {
      const startTime = Date.now();
      const generatedInsight = await generateAIInsight(excelData, insightType, chartId);
      const generationTime = Date.now() - startTime;

      // Update the insight with generated content
      aiInsight.title = generatedInsight.title;
      aiInsight.content = generatedInsight.content;
      aiInsight.keyFindings = generatedInsight.keyFindings;
      aiInsight.recommendations = generatedInsight.recommendations;
      aiInsight.confidence = generatedInsight.confidence;
      aiInsight.generationTime = generationTime;
      aiInsight.status = 'completed';
      aiInsight.metadata = {
        dataPoints: excelData.totalRows,
        columnsAnalyzed: excelData.columns.map(col => col.name),
        algorithm: 'mock-ai-v1',
        parameters: { insightType }
      };

      await aiInsight.save();

      res.status(201).json({
        success: true,
        message: 'AI insight generated successfully',
        data: aiInsight
      });

    } catch (generationError) {
      console.error('AI insight generation error:', generationError);
      
      // Update status to error
      aiInsight.status = 'error';
      aiInsight.errorMessage = generationError.message;
      await aiInsight.save();

      res.status(400).json({
        success: false,
        message: 'Failed to generate AI insight',
        error: generationError.message
      });
    }

  } catch (error) {
    console.error('Create AI insight error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create AI insight'
    });
  }
});

module.exports = router;
