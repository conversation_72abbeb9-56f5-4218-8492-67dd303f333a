{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-free": "^6.7.2", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "chart.js": "^4.4.9", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "CI=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "vercel-build": "react-scripts build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.5", "tailwindcss": "^3.4.17"}, "proxy": "http://localhost:5000"}