@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html, body, #root {
    @apply font-sans h-full;
    scroll-behavior: smooth;
  }
  
  body {
    @apply m-0 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 antialiased min-h-screen;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: flex;
    flex-direction: column;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-poppins font-semibold dark:text-white;
  }
  
  h1 {
    @apply text-3xl md:text-4xl;
  }
  
  h2 {
    @apply text-2xl md:text-3xl;
  }
  
  h3 {
    @apply text-xl md:text-2xl;
  }
  
  h4 {
    @apply text-lg md:text-xl;
  }
}

@layer components {
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-card p-6 transition-all duration-300 hover:shadow-card-hover dark:border dark:border-gray-700;
  }
  
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-2 focus:ring-primary-300 focus:outline-none dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-400;
  }
  
  .btn-secondary {
    @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-2 focus:ring-secondary-300 focus:outline-none dark:bg-secondary-600 dark:hover:bg-secondary-700 dark:focus:ring-secondary-400;
  }
  
  .btn-outline {
    @apply border border-gray-300 dark:border-gray-600 bg-transparent hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700 focus:outline-none dark:text-gray-200;
  }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
