const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

let mongod = null;

// Function to connect to database
const connectDB = async () => {
  try {
    let mongoUri = process.env.MONGO_URI;

    // If no MongoDB URI is provided or it's localhost, use in-memory database
    if (!mongoUri || mongoUri.includes('localhost') || process.env.NODE_ENV === 'test') {
      console.log('Starting in-memory MongoDB server...');
      
      // Create in-memory MongoDB instance
      mongod = await MongoMemoryServer.create({
        instance: {
          port: 27017, // Use default MongoDB port
          dbName: 'excel_analytics_local'
        }
      });
      
      mongoUri = mongod.getUri();
      console.log('In-memory MongoDB server started at:', mongoUri);
    }

    // Connect to MongoDB
    const conn = await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error('Database connection error:', error);
    
    // If Atlas connection fails, fallback to in-memory database
    if (error.message.includes('Atlas') || error.message.includes('whitelist') || error.message.includes('ECONNREFUSED')) {
      console.log('Falling back to in-memory database...');
      
      try {
        // Create in-memory MongoDB instance
        mongod = await MongoMemoryServer.create({
          instance: {
            port: 27018, // Use different port to avoid conflicts
            dbName: 'excel_analytics_fallback'
          }
        });
        
        const mongoUri = mongod.getUri();
        console.log('Fallback in-memory MongoDB server started at:', mongoUri);
        
        const conn = await mongoose.connect(mongoUri, {
          useNewUrlParser: true,
          useUnifiedTopology: true,
        });

        console.log(`Fallback MongoDB Connected: ${conn.connection.host}`);
        return conn;
      } catch (fallbackError) {
        console.error('Fallback database connection error:', fallbackError);
        process.exit(1);
      }
    } else {
      process.exit(1);
    }
  }
};

// Function to disconnect from database
const disconnectDB = async () => {
  try {
    await mongoose.connection.close();
    
    if (mongod) {
      await mongod.stop();
      console.log('In-memory MongoDB server stopped');
    }
    
    console.log('MongoDB disconnected');
  } catch (error) {
    console.error('Error disconnecting from database:', error);
  }
};

// Function to clear database (useful for testing)
const clearDB = async () => {
  try {
    const collections = mongoose.connection.collections;
    
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
    
    console.log('Database cleared');
  } catch (error) {
    console.error('Error clearing database:', error);
  }
};

// Function to seed database with sample data
const seedDB = async () => {
  try {
    const User = require('../models/User');
    
    // Check if admin user exists
    const adminExists = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminExists) {
      // Create admin user
      const adminUser = new User({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      });
      
      await adminUser.save();
      console.log('Admin user created: <EMAIL> / admin123');
    }
    
    // Check if demo user exists
    const demoExists = await User.findOne({ email: '<EMAIL>' });
    
    if (!demoExists) {
      // Create demo user
      const demoUser = new User({
        name: 'Demo User',
        email: '<EMAIL>',
        password: 'demo123',
        role: 'user'
      });
      
      await demoUser.save();
      console.log('Demo user created: <EMAIL> / demo123');
    }
    
    console.log('Database seeded successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
  }
};

module.exports = {
  connectDB,
  disconnectDB,
  clearDB,
  seedDB
};
