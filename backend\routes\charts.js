const express = require('express');
const { body, validationResult } = require('express-validator');
const Chart = require('../models/Chart');
const ExcelData = require('../models/ExcelData');
const { protect, checkOwnership } = require('../middleware/auth');

const router = express.Router();

// Function to generate colors for charts
const generateColors = (count, alpha = 1) => {
  const colors = [
    `rgba(255, 99, 132, ${alpha})`,
    `rgba(54, 162, 235, ${alpha})`,
    `rgba(255, 205, 86, ${alpha})`,
    `rgba(75, 192, 192, ${alpha})`,
    `rgba(153, 102, 255, ${alpha})`,
    `rgba(255, 159, 64, ${alpha})`,
    `rgba(199, 199, 199, ${alpha})`,
    `rgba(83, 102, 255, ${alpha})`,
    `rgba(255, 99, 255, ${alpha})`,
    `rgba(99, 255, 132, ${alpha})`
  ];
  
  const result = [];
  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }
  return result;
};

// Process bar/line/area charts
const processBarLineChart = (data, config) => {
  const { xAxis, yAxis, categoryField, aggregationMethod = 'sum' } = config;
  
  if (!xAxis || xAxis.length === 0 || !yAxis || yAxis.length === 0) {
    throw new Error('X-axis and Y-axis fields are required for bar/line charts');
  }

  const xField = xAxis[0];
  const yField = yAxis[0];

  // Group data by x-axis field
  const groupedData = {};
  
  data.forEach(row => {
    const xValue = row[xField];
    const yValue = parseFloat(row[yField]) || 0;
    
    if (xValue !== null && xValue !== undefined) {
      if (!groupedData[xValue]) {
        groupedData[xValue] = [];
      }
      groupedData[xValue].push(yValue);
    }
  });

  // Apply aggregation
  const labels = Object.keys(groupedData);
  const aggregatedData = labels.map(label => {
    const values = groupedData[label];
    switch (aggregationMethod) {
      case 'sum':
        return values.reduce((sum, val) => sum + val, 0);
      case 'average':
        return values.reduce((sum, val) => sum + val, 0) / values.length;
      case 'count':
        return values.length;
      case 'min':
        return Math.min(...values);
      case 'max':
        return Math.max(...values);
      default:
        return values.reduce((sum, val) => sum + val, 0);
    }
  });

  return {
    labels,
    datasets: [{
      label: yField,
      data: aggregatedData,
      backgroundColor: generateColors(labels.length, 0.6),
      borderColor: generateColors(labels.length, 1),
      borderWidth: 2
    }]
  };
};

// Process pie/doughnut charts
const processPieChart = (data, config) => {
  const { xAxis, yAxis, aggregationMethod = 'sum' } = config;
  
  if (!xAxis || xAxis.length === 0) {
    throw new Error('Category field is required for pie charts');
  }

  const categoryField = xAxis[0];
  const valueField = yAxis && yAxis.length > 0 ? yAxis[0] : null;

  // Group data by category
  const groupedData = {};
  
  data.forEach(row => {
    const category = row[categoryField];
    
    if (category !== null && category !== undefined) {
      if (!groupedData[category]) {
        groupedData[category] = [];
      }
      
      if (valueField) {
        const value = parseFloat(row[valueField]) || 0;
        groupedData[category].push(value);
      } else {
        groupedData[category].push(1); // Count occurrences
      }
    }
  });

  // Apply aggregation
  const labels = Object.keys(groupedData);
  const aggregatedData = labels.map(label => {
    const values = groupedData[label];
    switch (aggregationMethod) {
      case 'sum':
        return values.reduce((sum, val) => sum + val, 0);
      case 'average':
        return values.reduce((sum, val) => sum + val, 0) / values.length;
      case 'count':
        return values.length;
      case 'min':
        return Math.min(...values);
      case 'max':
        return Math.max(...values);
      default:
        return values.reduce((sum, val) => sum + val, 0);
    }
  });

  return {
    labels,
    datasets: [{
      data: aggregatedData,
      backgroundColor: generateColors(labels.length, 0.8),
      borderColor: generateColors(labels.length, 1),
      borderWidth: 1
    }]
  };
};

// Process scatter/bubble charts
const processScatterChart = (data, config) => {
  const { xAxis, yAxis, categoryField, sizeField } = config;
  
  if (!xAxis || xAxis.length === 0 || !yAxis || yAxis.length === 0) {
    throw new Error('X-axis and Y-axis fields are required for scatter charts');
  }

  const xField = xAxis[0];
  const yField = yAxis[0];

  const scatterData = data.map(row => {
    const point = {
      x: parseFloat(row[xField]) || 0,
      y: parseFloat(row[yField]) || 0
    };
    
    if (sizeField && config.type === 'bubble') {
      point.r = parseFloat(row[sizeField]) || 5;
    }
    
    return point;
  }).filter(point => !isNaN(point.x) && !isNaN(point.y));

  return {
    datasets: [{
      label: `${xField} vs ${yField}`,
      data: scatterData,
      backgroundColor: generateColors(1, 0.6)[0],
      borderColor: generateColors(1, 1)[0],
      borderWidth: 1
    }]
  };
};

// Function to process chart data based on configuration
const processChartData = (excelData, config) => {
  try {
    const { data } = excelData;
    const { type, xAxis, yAxis, categoryField, sizeField } = config;

    if (!data || data.length === 0) {
      throw new Error('No data available for chart generation');
    }

    // Validate required fields exist in data
    const availableColumns = Object.keys(data[0] || {});
    const requiredFields = [...(xAxis || []), ...(yAxis || [])];
    
    if (categoryField) requiredFields.push(categoryField);
    if (sizeField) requiredFields.push(sizeField);

    const missingFields = requiredFields.filter(field => !availableColumns.includes(field));
    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }

    let processedData = {};

    switch (type) {
      case 'bar':
      case 'line':
      case 'area':
        processedData = processBarLineChart(data, config);
        break;
      case 'pie':
      case 'doughnut':
        processedData = processPieChart(data, config);
        break;
      case 'scatter':
      case 'bubble':
        processedData = processScatterChart(data, config);
        break;
      default:
        throw new Error(`Unsupported chart type: ${type}`);
    }

    return processedData;
  } catch (error) {
    console.error('Chart data processing error:', error);
    throw error;
  }
};

// @desc    Preview chart data
// @route   POST /api/charts/preview
// @access  Private
router.post('/preview', protect, [
  body('excelDataId')
    .notEmpty()
    .withMessage('Excel data ID is required'),
  body('config.type')
    .isIn(['bar', 'line', 'pie', 'doughnut', 'scatter', 'bubble', 'area', 'radar', 'polarArea'])
    .withMessage('Invalid chart type'),
  body('config.xAxis')
    .isArray({ min: 1 })
    .withMessage('At least one X-axis field is required'),
  body('config.yAxis')
    .optional()
    .isArray()
    .withMessage('Y-axis must be an array')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { excelDataId, config } = req.body;

    // Get Excel data
    const excelData = await ExcelData.findById(excelDataId);
    if (!excelData) {
      return res.status(404).json({
        success: false,
        message: 'Excel data not found'
      });
    }

    // Check if user owns the Excel data or is admin
    if (excelData.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this data'
      });
    }

    // Process chart data
    const processedData = processChartData(excelData, config);

    res.json({
      success: true,
      data: processedData
    });

  } catch (error) {
    console.error('Chart preview error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to generate chart preview'
    });
  }
});

// @desc    Create new chart
// @route   POST /api/charts
// @access  Private
router.post('/', protect, [
  body('title')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Title must be between 1 and 100 characters'),
  body('type')
    .isIn(['bar', 'line', 'pie', 'doughnut', 'scatter', 'bubble', 'area', 'radar', 'polarArea'])
    .withMessage('Invalid chart type'),
  body('excelDataId')
    .notEmpty()
    .withMessage('Excel data ID is required'),
  body('config.xAxis')
    .isArray({ min: 1 })
    .withMessage('At least one X-axis field is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { title, description, type, excelDataId, config } = req.body;

    // Get Excel data
    const excelData = await ExcelData.findById(excelDataId);
    if (!excelData) {
      return res.status(404).json({
        success: false,
        message: 'Excel data not found'
      });
    }

    // Check if user owns the Excel data or is admin
    if (excelData.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this data'
      });
    }

    // Process chart data
    const processedData = processChartData(excelData, config);

    // Create chart
    const chart = new Chart({
      title,
      description: description || '',
      type,
      user: req.user._id,
      excelData: excelDataId,
      config,
      processedData,
      status: 'draft'
    });

    await chart.save();

    res.status(201).json({
      success: true,
      message: 'Chart created successfully',
      data: chart
    });

  } catch (error) {
    console.error('Create chart error:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to create chart'
    });
  }
});

// @desc    Get all charts for user
// @route   GET /api/charts
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    const { page = 1, limit = 10, type, status, search } = req.query;

    const query = { user: req.user._id };

    // Filter by type if provided
    if (type) {
      query.type = type;
    }

    // Filter by status if provided
    if (status) {
      query.status = status;
    }

    // Search in title or description
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const charts = await Chart.find(query)
      .populate('excelData', 'filename originalName')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select('-processedData'); // Exclude large data field

    const total = await Chart.countDocuments(query);

    res.json({
      success: true,
      data: charts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve charts'
    });
  }
});

// @desc    Get single chart
// @route   GET /api/charts/:id
// @access  Private
router.get('/:id', protect, async (req, res) => {
  try {
    const chart = await Chart.findById(req.params.id)
      .populate('excelData', 'filename originalName columns')
      .populate('user', 'name email');

    if (!chart) {
      return res.status(404).json({
        success: false,
        message: 'Chart not found'
      });
    }

    // Check if user owns the chart or chart is public or user is admin
    if (chart.user._id.toString() !== req.user._id.toString() &&
        !chart.isPublic &&
        req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this chart'
      });
    }

    // Increment view count if not owner
    if (chart.user._id.toString() !== req.user._id.toString()) {
      await chart.incrementViewCount();
    }

    res.json({
      success: true,
      data: chart
    });

  } catch (error) {
    console.error('Get chart error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve chart'
    });
  }
});

module.exports = router;
