const mongoose = require('mongoose');

const chartSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Chart title is required'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  type: {
    type: String,
    required: [true, 'Chart type is required'],
    enum: ['bar', 'line', 'pie', 'doughnut', 'scatter', 'bubble', 'area', 'radar', 'polarArea'],
    default: 'bar'
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  excelData: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ExcelData',
    required: [true, 'Excel data is required']
  },
  config: {
    xAxis: [{
      type: String,
      trim: true
    }],
    yAxis: [{
      type: String,
      trim: true
    }],
    categoryField: {
      type: String,
      trim: true
    },
    sizeField: {
      type: String,
      trim: true
    },
    aggregationMethod: {
      type: String,
      enum: ['sum', 'average', 'count', 'min', 'max'],
      default: 'sum'
    },
    colors: [{
      type: String,
      trim: true
    }],
    showLegend: {
      type: Boolean,
      default: true
    },
    showGrid: {
      type: Boolean,
      default: true
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    animation: {
      type: Boolean,
      default: true
    }
  },
  processedData: {
    labels: [mongoose.Schema.Types.Mixed],
    datasets: [{
      label: String,
      data: [mongoose.Schema.Types.Mixed],
      backgroundColor: [String],
      borderColor: [String],
      borderWidth: Number
    }]
  },
  chartOptions: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  thumbnail: {
    type: String,
    default: ''
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true
  }],
  viewCount: {
    type: Number,
    default: 0
  },
  likeCount: {
    type: Number,
    default: 0
  },
  shareCount: {
    type: Number,
    default: 0
  },
  lastViewed: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for better query performance
chartSchema.index({ user: 1, createdAt: -1 });
chartSchema.index({ excelData: 1 });
chartSchema.index({ type: 1 });
chartSchema.index({ isPublic: 1, status: 1 });
chartSchema.index({ tags: 1 });

// Pre-save middleware to update updatedAt
chartSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance method to increment view count
chartSchema.methods.incrementViewCount = function() {
  this.viewCount += 1;
  this.lastViewed = Date.now();
  return this.save();
};

// Instance method to get summary
chartSchema.methods.getSummary = function() {
  return {
    _id: this._id,
    title: this.title,
    description: this.description,
    type: this.type,
    thumbnail: this.thumbnail,
    viewCount: this.viewCount,
    likeCount: this.likeCount,
    status: this.status,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt
  };
};

// Static method to find by user
chartSchema.statics.findByUser = function(userId) {
  return this.find({ user: userId }).populate('excelData', 'filename originalName').sort({ createdAt: -1 });
};

// Static method to find public charts
chartSchema.statics.findPublic = function() {
  return this.find({ isPublic: true, status: 'published' })
    .populate('user', 'name')
    .populate('excelData', 'filename originalName')
    .sort({ createdAt: -1 });
};

module.exports = mongoose.model('Chart', chartSchema);
