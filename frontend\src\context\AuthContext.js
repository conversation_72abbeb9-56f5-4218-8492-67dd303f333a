import React, { createContext, useState, useEffect } from 'react';

const AuthContext = createContext();

// Pre-registered user for local authentication
const PRE_REGISTERED_USER = {
  id: 'user_001',
  name: 'Demo User',
  email: '<EMAIL>',
  password: 'demo123',
  role: 'user',
  isActive: true,
  createdAt: new Date().toISOString(),
  lastLogin: null,
  preferences: {
    theme: 'light',
    notifications: true
  }
};

// Generate a simple token for local authentication
const generateLocalToken = (userId) => {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2);
  return `local_${userId}_${timestamp}_${randomString}`;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token') || null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize pre-registered user in localStorage if not exists
  useEffect(() => {
    const initializeLocalUser = () => {
      const existingUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');
      const userExists = existingUsers.find(u => u.email === PRE_REGISTERED_USER.email);

      if (!userExists) {
        const updatedUsers = [...existingUsers, PRE_REGISTERED_USER];
        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));
        console.log('Pre-registered user initialized:', PRE_REGISTERED_USER.email);
        console.log('Login credentials - Email:', PRE_REGISTERED_USER.email, 'Password:', PRE_REGISTERED_USER.password);
      }
    };

    initializeLocalUser();
  }, []);

  // Auto-login user on initial load (no login required)
  useEffect(() => {
    const autoLogin = () => {
      try {
        const storedUser = localStorage.getItem('user');
        const storedToken = localStorage.getItem('token');

        if (storedUser && storedToken) {
          // For local authentication, we trust the stored data
          setToken(storedToken);
          setUser(JSON.parse(storedUser));
          console.log('User loaded from localStorage:', JSON.parse(storedUser).email);
        } else {
          // Auto-login with pre-registered user
          performAutoLogin();
        }
      } catch (err) {
        console.error('Error loading user:', err);
        // Clear corrupted data and auto-login
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        performAutoLogin();
      } finally {
        setLoading(false);
      }
    };

    const performAutoLogin = () => {
      const autoToken = generateLocalToken(PRE_REGISTERED_USER.id);
      const autoUser = { ...PRE_REGISTERED_USER, lastLogin: new Date().toISOString() };

      setUser(autoUser);
      setToken(autoToken);
      localStorage.setItem('token', autoToken);
      localStorage.setItem('user', JSON.stringify(autoUser));
      console.log('Auto-logged in as:', autoUser.email);
    };

    autoLogin();
  }, []);

  // Register user (local storage based)
  const register = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      const { name, email, password } = userData;

      // Check if user already exists
      const existingUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');
      const userExists = existingUsers.find(u => u.email === email);

      if (userExists) {
        setError('User already exists with this email');
        return false;
      }

      // Create new user
      const newUser = {
        id: `user_${Date.now()}`,
        name,
        email,
        password,
        role: 'user',
        isActive: true,
        createdAt: new Date().toISOString(),
        lastLogin: null,
        preferences: {
          theme: 'light',
          notifications: true
        }
      };

      // Add to local users
      const updatedUsers = [...existingUsers, newUser];
      localStorage.setItem('localUsers', JSON.stringify(updatedUsers));

      // Generate token and login
      const token = generateLocalToken(newUser.id);
      const userProfile = { ...newUser };
      delete userProfile.password; // Remove password from profile

      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(userProfile));

      setToken(token);
      setUser(userProfile);

      console.log('User registered successfully:', email);
      return true;
    } catch (err) {
      console.error('Registration error:', err);
      setError('Registration failed');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Login user (local storage based)
  const login = async (email, password) => {
    setLoading(true);
    setError(null);

    try {
      console.log('Local login attempt:', { email, password: '********' });

      // Get users from local storage
      const existingUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');
      const user = existingUsers.find(u => u.email === email && u.password === password);

      if (!user) {
        setError('Invalid email or password');
        return false;
      }

      if (!user.isActive) {
        setError('Account is deactivated');
        return false;
      }

      // Update last login
      user.lastLogin = new Date().toISOString();
      const updatedUsers = existingUsers.map(u => u.id === user.id ? user : u);
      localStorage.setItem('localUsers', JSON.stringify(updatedUsers));

      // Generate token and create user profile
      const token = generateLocalToken(user.id);
      const userProfile = { ...user };
      delete userProfile.password; // Remove password from profile

      // Store token and user in localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(userProfile));

      // Update context state
      setToken(token);
      setUser(userProfile);

      console.log('Local login successful:', email);
      return true;
    } catch (err) {
      console.error('Login error:', err);
      setError('Login failed');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setToken(null);
    setUser(null);
  };

  // Update user profile (local storage based)
  const updateUserProfile = async (userId, profileData) => {
    setLoading(true);
    setError(null);
    try {
      // Get users from local storage
      const existingUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');
      const userIndex = existingUsers.findIndex(u => u.id === userId);

      if (userIndex === -1) {
        setError('User not found');
        return { success: false, error: 'User not found' };
      }

      // Update user data
      const updatedUser = { ...existingUsers[userIndex], ...profileData };
      existingUsers[userIndex] = updatedUser;

      // Save to local storage
      localStorage.setItem('localUsers', JSON.stringify(existingUsers));

      // Update current user state (remove password from profile)
      const userProfile = { ...updatedUser };
      delete userProfile.password;
      setUser(userProfile);
      localStorage.setItem('user', JSON.stringify(userProfile));

      return { success: true, data: userProfile };
    } catch (err) {
      console.error('Profile update error:', err);
      setError('Failed to update profile');
      return { success: false, error: 'Failed to update profile' };
    } finally {
      setLoading(false);
    }
  };

  // Update user preferences (local storage based)
  const updateUserPreferences = async (userId, preferencesData) => {
    setLoading(true);
    setError(null);
    try {
      // Get users from local storage
      const existingUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');
      const userIndex = existingUsers.findIndex(u => u.id === userId);

      if (userIndex === -1) {
        setError('User not found');
        return { success: false, error: 'User not found' };
      }

      // Update user preferences
      const updatedUser = {
        ...existingUsers[userIndex],
        preferences: { ...existingUsers[userIndex].preferences, ...preferencesData }
      };
      existingUsers[userIndex] = updatedUser;

      // Save to local storage
      localStorage.setItem('localUsers', JSON.stringify(existingUsers));

      // Update current user state (remove password from profile)
      const userProfile = { ...updatedUser };
      delete userProfile.password;
      setUser(userProfile);
      localStorage.setItem('user', JSON.stringify(userProfile));

      // Dispatch a custom event to notify other components of the preference change
      window.dispatchEvent(new CustomEvent('userPreferencesChanged', {
        detail: { preferences: userProfile.preferences }
      }));

      return { success: true, data: { preferences: userProfile.preferences } };
    } catch (err) {
      console.error('Preferences update error:', err);
      setError('Failed to update preferences');
      return { success: false, error: 'Failed to update preferences' };
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        loading,
        error,
        register,
        login,
        logout,
        updateUserProfile,
        updateUserPreferences,
        isAuthenticated: !!token,
        isAdmin: user?.role === 'admin'
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;