import { useEffect } from 'react';

/**
 * Hook that alerts when you click outside of the passed ref
 * @param {React.RefObject} ref - The ref object to detect clicks outside of
 * @param {Function} handler - The callback function to run when a click outside is detected
 */
export const useOnClickOutside = (ref, handler) => {
  useEffect(() => {
    const listener = (event) => {
      // Do nothing if clicking ref's element or descendent elements
      if (!ref.current || ref.current.contains(event.target)) {
        return;
      }
      
      handler(event);
    };
    
    document.addEventListener('mousedown', listener);
    document.addEventListener('touchstart', listener);
    
    return () => {
      document.removeEventListener('mousedown', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, handler]);
};