const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

// Function to detect column data type
const detectColumnType = (values) => {
  const nonNullValues = values.filter(val => val !== null && val !== undefined && val !== '');
  
  if (nonNullValues.length === 0) return 'string';
  
  let numberCount = 0;
  let dateCount = 0;
  let booleanCount = 0;
  
  for (const value of nonNullValues) {
    // Check if it's a number
    if (typeof value === 'number' || (!isNaN(value) && !isNaN(parseFloat(value)))) {
      numberCount++;
    }
    // Check if it's a date
    else if (value instanceof Date || (!isNaN(Date.parse(value)) && isNaN(value))) {
      dateCount++;
    }
    // Check if it's a boolean
    else if (typeof value === 'boolean' || 
             value.toString().toLowerCase() === 'true' || 
             value.toString().toLowerCase() === 'false') {
      booleanCount++;
    }
  }
  
  const total = nonNullValues.length;
  const threshold = 0.7; // 70% threshold
  
  if (numberCount / total >= threshold) return 'number';
  if (dateCount / total >= threshold) return 'date';
  if (booleanCount / total >= threshold) return 'boolean';
  
  return 'string';
};

// Function to get sample values for a column
const getSampleValues = (values, maxSamples = 5) => {
  const nonNullValues = values.filter(val => val !== null && val !== undefined && val !== '');
  const uniqueValues = [...new Set(nonNullValues)];
  return uniqueValues.slice(0, maxSamples);
};

// Function to process Excel file
const processExcelFile = async (filePath) => {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error('File not found');
    }

    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    
    // Get sheet names
    const sheetNames = workbook.SheetNames;
    
    if (sheetNames.length === 0) {
      throw new Error('No sheets found in the Excel file');
    }

    // Process the first sheet (you can modify this to process all sheets)
    const firstSheetName = sheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    
    // Convert sheet to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1, // Use array of arrays format
      defval: null // Default value for empty cells
    });
    
    if (jsonData.length === 0) {
      throw new Error('Sheet is empty');
    }

    // Assume first row contains headers
    const headers = jsonData[0];
    const dataRows = jsonData.slice(1);
    
    // Filter out completely empty rows
    const filteredDataRows = dataRows.filter(row => 
      row.some(cell => cell !== null && cell !== undefined && cell !== '')
    );

    // Process columns
    const columns = headers.map((header, index) => {
      const columnValues = filteredDataRows.map(row => row[index]);
      
      return {
        name: header || `Column_${index + 1}`,
        type: detectColumnType(columnValues),
        sampleValues: getSampleValues(columnValues)
      };
    });

    // Convert data to object format with headers as keys
    const processedData = filteredDataRows.map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        const key = header || `Column_${index + 1}`;
        obj[key] = row[index];
      });
      return obj;
    });

    // Calculate statistics
    const totalRows = filteredDataRows.length;
    const totalColumns = headers.length;

    // Generate summary statistics for numeric columns
    const numericStats = {};
    columns.forEach(column => {
      if (column.type === 'number') {
        const values = processedData
          .map(row => row[column.name])
          .filter(val => val !== null && val !== undefined && !isNaN(val))
          .map(val => parseFloat(val));
        
        if (values.length > 0) {
          numericStats[column.name] = {
            count: values.length,
            min: Math.min(...values),
            max: Math.max(...values),
            avg: values.reduce((sum, val) => sum + val, 0) / values.length,
            sum: values.reduce((sum, val) => sum + val, 0)
          };
        }
      }
    });

    return {
      sheetNames,
      totalRows,
      totalColumns,
      columns,
      data: processedData,
      processedData: {
        summary: {
          totalRows,
          totalColumns,
          numericColumns: columns.filter(col => col.type === 'number').length,
          dateColumns: columns.filter(col => col.type === 'date').length,
          textColumns: columns.filter(col => col.type === 'string').length
        },
        statistics: numericStats,
        columnTypes: columns.reduce((acc, col) => {
          acc[col.name] = col.type;
          return acc;
        }, {})
      },
      metadata: {
        hasHeaders: true,
        encoding: 'utf8',
        delimiter: ',',
        dateFormat: 'MM/DD/YYYY'
      }
    };

  } catch (error) {
    console.error('Excel processing error:', error);
    throw new Error(`Failed to process Excel file: ${error.message}`);
  }
};

// Function to validate Excel file
const validateExcelFile = (file) => {
  const allowedMimeTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel.sheet.macroEnabled.12'
  ];

  const allowedExtensions = ['.xls', '.xlsx', '.xlsm'];
  const fileExtension = path.extname(file.originalname).toLowerCase();

  if (!allowedMimeTypes.includes(file.mimetype) && !allowedExtensions.includes(fileExtension)) {
    throw new Error('Invalid file type. Please upload an Excel file (.xls, .xlsx, .xlsm)');
  }

  // Check file size (10MB limit)
  const maxSize = parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    throw new Error(`File size too large. Maximum size allowed is ${maxSize / (1024 * 1024)}MB`);
  }

  return true;
};

// Function to generate unique filename
const generateUniqueFilename = (originalName) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  const extension = path.extname(originalName);
  const baseName = path.basename(originalName, extension);
  
  return `${baseName}_${timestamp}_${random}${extension}`;
};

module.exports = {
  processExcelFile,
  validateExcelFile,
  generateUniqueFilename,
  detectColumnType,
  getSampleValues
};
