const mongoose = require('mongoose');

const aiInsightSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Insight title is required'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  content: {
    type: String,
    required: [true, 'Insight content is required'],
    trim: true
  },
  insightType: {
    type: String,
    required: [true, 'Insight type is required'],
    enum: ['summary', 'trend', 'anomaly', 'correlation', 'prediction', 'recommendation'],
    default: 'summary'
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  excelData: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ExcelData',
    required: [true, 'Excel data is required']
  },
  chart: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Chart'
  },
  confidence: {
    type: Number,
    min: 0,
    max: 1,
    default: 0.5
  },
  metadata: {
    dataPoints: {
      type: Number,
      default: 0
    },
    columnsAnalyzed: [{
      type: String,
      trim: true
    }],
    timeRange: {
      start: Date,
      end: Date
    },
    algorithm: {
      type: String,
      trim: true
    },
    parameters: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },
  visualizations: [{
    type: {
      type: String,
      enum: ['chart', 'table', 'heatmap', 'scatter', 'histogram']
    },
    data: mongoose.Schema.Types.Mixed,
    config: mongoose.Schema.Types.Mixed
  }],
  keyFindings: [{
    finding: {
      type: String,
      required: true,
      trim: true
    },
    importance: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    value: mongoose.Schema.Types.Mixed
  }],
  recommendations: [{
    action: {
      type: String,
      required: true,
      trim: true
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium'
    },
    impact: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    effort: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    }
  }],
  tags: [{
    type: String,
    trim: true
  }],
  isBookmarked: {
    type: Boolean,
    default: false
  },
  isShared: {
    type: Boolean,
    default: false
  },
  shareUrl: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['generating', 'completed', 'error', 'archived'],
    default: 'generating'
  },
  errorMessage: {
    type: String,
    trim: true
  },
  generationTime: {
    type: Number,
    default: 0
  },
  lastAccessed: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for better query performance
aiInsightSchema.index({ user: 1, createdAt: -1 });
aiInsightSchema.index({ excelData: 1 });
aiInsightSchema.index({ chart: 1 });
aiInsightSchema.index({ insightType: 1 });
aiInsightSchema.index({ status: 1 });
aiInsightSchema.index({ tags: 1 });

// Pre-save middleware to update updatedAt
aiInsightSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance method to get summary
aiInsightSchema.methods.getSummary = function() {
  return {
    _id: this._id,
    title: this.title,
    insightType: this.insightType,
    confidence: this.confidence,
    status: this.status,
    isBookmarked: this.isBookmarked,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt
  };
};

// Static method to find by user
aiInsightSchema.statics.findByUser = function(userId) {
  return this.find({ user: userId })
    .populate('excelData', 'filename originalName')
    .populate('chart', 'title type')
    .sort({ createdAt: -1 });
};

// Static method to find by excel data
aiInsightSchema.statics.findByExcelData = function(excelDataId) {
  return this.find({ excelData: excelDataId })
    .populate('chart', 'title type')
    .sort({ createdAt: -1 });
};

module.exports = mongoose.model('AIInsight', aiInsightSchema);
