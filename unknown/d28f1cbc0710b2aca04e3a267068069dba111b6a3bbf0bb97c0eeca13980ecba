const express = require('express');
const ExcelData = require('../models/ExcelData');
const Chart = require('../models/Chart');
const AIInsight = require('../models/AIInsight');
const User = require('../models/User');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// @desc    Get user dashboard data
// @route   GET /api/dashboard
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    const userId = req.user._id;

    // Get file statistics
    const totalFiles = await ExcelData.countDocuments({ user: userId });
    const completedFiles = await ExcelData.countDocuments({ user: userId, status: 'completed' });
    const processingFiles = await ExcelData.countDocuments({ user: userId, status: 'processing' });
    const errorFiles = await ExcelData.countDocuments({ user: userId, status: 'error' });

    // Get chart statistics
    const totalCharts = await Chart.countDocuments({ user: userId });
    const publishedCharts = await Chart.countDocuments({ user: userId, status: 'published' });
    const draftCharts = await Chart.countDocuments({ user: userId, status: 'draft' });

    // Get AI insight statistics
    const totalInsights = await AIInsight.countDocuments({ user: userId });
    const completedInsights = await AIInsight.countDocuments({ user: userId, status: 'completed' });

    // Get recent files (last 5)
    const recentFiles = await ExcelData.find({ user: userId })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('filename originalName fileSize status createdAt totalRows totalColumns');

    // Get recent charts (last 5)
    const recentCharts = await Chart.find({ user: userId })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title type status viewCount createdAt')
      .populate('excelData', 'filename originalName');

    // Get recent insights (last 5)
    const recentInsights = await AIInsight.find({ user: userId })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title insightType confidence status createdAt')
      .populate('excelData', 'filename originalName');

    // Calculate file size statistics
    const fileSizeStats = await ExcelData.aggregate([
      { $match: { user: userId, status: 'completed' } },
      {
        $group: {
          _id: null,
          totalSize: { $sum: '$fileSize' },
          avgSize: { $avg: '$fileSize' },
          maxSize: { $max: '$fileSize' },
          minSize: { $min: '$fileSize' }
        }
      }
    ]);

    // Calculate data volume statistics
    const dataVolumeStats = await ExcelData.aggregate([
      { $match: { user: userId, status: 'completed' } },
      {
        $group: {
          _id: null,
          totalRows: { $sum: '$totalRows' },
          totalColumns: { $sum: '$totalColumns' },
          avgRows: { $avg: '$totalRows' },
          avgColumns: { $avg: '$totalColumns' }
        }
      }
    ]);

    // Get chart type distribution
    const chartTypeDistribution = await Chart.aggregate([
      { $match: { user: userId } },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get monthly activity (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyActivity = await ExcelData.aggregate([
      {
        $match: {
          user: userId,
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          fileUploads: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Format monthly activity data
    const formattedMonthlyActivity = monthlyActivity.map(item => ({
      month: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`,
      fileUploads: item.fileUploads
    }));

    const dashboardData = {
      statistics: {
        files: {
          total: totalFiles,
          completed: completedFiles,
          processing: processingFiles,
          error: errorFiles
        },
        charts: {
          total: totalCharts,
          published: publishedCharts,
          draft: draftCharts
        },
        insights: {
          total: totalInsights,
          completed: completedInsights
        }
      },
      recentActivity: {
        files: recentFiles,
        charts: recentCharts,
        insights: recentInsights
      },
      analytics: {
        fileSize: fileSizeStats[0] || {
          totalSize: 0,
          avgSize: 0,
          maxSize: 0,
          minSize: 0
        },
        dataVolume: dataVolumeStats[0] || {
          totalRows: 0,
          totalColumns: 0,
          avgRows: 0,
          avgColumns: 0
        },
        chartTypes: chartTypeDistribution,
        monthlyActivity: formattedMonthlyActivity
      },
      user: {
        name: req.user.name,
        email: req.user.email,
        role: req.user.role,
        lastLogin: req.user.lastLogin,
        memberSince: req.user.createdAt
      }
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load dashboard data'
    });
  }
});

// @desc    Get admin dashboard data
// @route   GET /api/dashboard/admin
// @access  Private (Admin only)
router.get('/admin', protect, authorize('admin'), async (req, res) => {
  try {
    // Get overall statistics
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const adminUsers = await User.countDocuments({ role: 'admin' });
    
    const totalFiles = await ExcelData.countDocuments();
    const completedFiles = await ExcelData.countDocuments({ status: 'completed' });
    const processingFiles = await ExcelData.countDocuments({ status: 'processing' });
    const errorFiles = await ExcelData.countDocuments({ status: 'error' });
    
    const totalCharts = await Chart.countDocuments();
    const publishedCharts = await Chart.countDocuments({ status: 'published' });
    const publicCharts = await Chart.countDocuments({ isPublic: true });
    
    const totalInsights = await AIInsight.countDocuments();
    const completedInsights = await AIInsight.countDocuments({ status: 'completed' });

    // Get recent users (last 10)
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .select('name email role isActive lastLogin createdAt');

    // Get top users by file count
    const topUsersByFiles = await ExcelData.aggregate([
      {
        $group: {
          _id: '$user',
          fileCount: { $sum: 1 }
        }
      },
      { $sort: { fileCount: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          name: '$user.name',
          email: '$user.email',
          fileCount: 1
        }
      }
    ]);

    // Get system-wide file size statistics
    const systemFileSizeStats = await ExcelData.aggregate([
      { $match: { status: 'completed' } },
      {
        $group: {
          _id: null,
          totalSize: { $sum: '$fileSize' },
          avgSize: { $avg: '$fileSize' },
          maxSize: { $max: '$fileSize' },
          minSize: { $min: '$fileSize' }
        }
      }
    ]);

    // Get daily activity for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyActivity = await ExcelData.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          fileUploads: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    const adminDashboardData = {
      statistics: {
        users: {
          total: totalUsers,
          active: activeUsers,
          admin: adminUsers
        },
        files: {
          total: totalFiles,
          completed: completedFiles,
          processing: processingFiles,
          error: errorFiles
        },
        charts: {
          total: totalCharts,
          published: publishedCharts,
          public: publicCharts
        },
        insights: {
          total: totalInsights,
          completed: completedInsights
        }
      },
      recentActivity: {
        users: recentUsers
      },
      analytics: {
        topUsers: topUsersByFiles,
        systemFileSize: systemFileSizeStats[0] || {
          totalSize: 0,
          avgSize: 0,
          maxSize: 0,
          minSize: 0
        },
        dailyActivity: dailyActivity.map(item => ({
          date: `${item._id.year}-${String(item._id.month).padStart(2, '0')}-${String(item._id.day).padStart(2, '0')}`,
          fileUploads: item.fileUploads
        }))
      }
    };

    res.json({
      success: true,
      data: adminDashboardData
    });

  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to load admin dashboard data'
    });
  }
});

module.exports = router;
