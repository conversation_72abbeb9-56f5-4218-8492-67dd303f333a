// Intelligent chart suggestion engine for Excel data
const suggestCharts = (excelData) => {
  const { data, columns, totalRows } = excelData;
  const suggestions = [];

  // Analyze column types
  const numericColumns = columns.filter(col => col.type === 'number');
  const textColumns = columns.filter(col => col.type === 'string');
  const dateColumns = columns.filter(col => col.type === 'date');

  // Analyze data characteristics
  const dataCharacteristics = analyzeDataCharacteristics(data, columns);

  // 1. Bar Chart Suggestions
  if (textColumns.length > 0 && numericColumns.length > 0) {
    textColumns.forEach(textCol => {
      numericColumns.forEach(numCol => {
        const uniqueValues = new Set(data.map(row => row[textCol.name])).size;
        
        // Suggest bar chart if categorical data has reasonable number of categories
        if (uniqueValues <= 20 && uniqueValues >= 2) {
          suggestions.push({
            type: 'bar',
            title: `${textCol.name} vs ${numCol.name}`,
            description: `Compare ${numCol.name} across different ${textCol.name} categories`,
            config: {
              xAxis: [textCol.name],
              yAxis: [numCol.name],
              aggregationMethod: 'sum'
            },
            priority: calculateChartPriority('bar', { uniqueValues, dataSize: totalRows }),
            reasoning: `Categorical data (${textCol.name}) with ${uniqueValues} categories is ideal for bar charts`
          });
        }
      });
    });
  }

  // 2. Pie Chart Suggestions
  if (textColumns.length > 0 && numericColumns.length > 0) {
    textColumns.forEach(textCol => {
      numericColumns.forEach(numCol => {
        const uniqueValues = new Set(data.map(row => row[textCol.name])).size;
        
        // Suggest pie chart for categorical data with fewer categories
        if (uniqueValues <= 8 && uniqueValues >= 2) {
          suggestions.push({
            type: 'pie',
            title: `Distribution of ${numCol.name} by ${textCol.name}`,
            description: `Show proportional distribution of ${numCol.name} across ${textCol.name}`,
            config: {
              valueField: numCol.name,
              labelField: textCol.name,
              aggregationMethod: 'sum'
            },
            priority: calculateChartPriority('pie', { uniqueValues, dataSize: totalRows }),
            reasoning: `${uniqueValues} categories in ${textCol.name} work well for pie chart visualization`
          });
        }
      });
    });
  }

  // 3. Line Chart Suggestions (for time series or sequential data)
  if (dateColumns.length > 0 && numericColumns.length > 0) {
    dateColumns.forEach(dateCol => {
      numericColumns.forEach(numCol => {
        suggestions.push({
          type: 'line',
          title: `${numCol.name} Trend Over Time`,
          description: `Track changes in ${numCol.name} over ${dateCol.name}`,
          config: {
            xAxis: [dateCol.name],
            yAxis: [numCol.name],
            aggregationMethod: 'average'
          },
          priority: calculateChartPriority('line', { hasDate: true, dataSize: totalRows }),
          reasoning: `Time-based data (${dateCol.name}) is perfect for trend analysis with line charts`
        });
      });
    });
  }

  // 4. Scatter Plot Suggestions (for correlation analysis)
  if (numericColumns.length >= 2) {
    for (let i = 0; i < numericColumns.length; i++) {
      for (let j = i + 1; j < numericColumns.length; j++) {
        const col1 = numericColumns[i];
        const col2 = numericColumns[j];
        
        suggestions.push({
          type: 'scatter',
          title: `${col1.name} vs ${col2.name} Correlation`,
          description: `Analyze relationship between ${col1.name} and ${col2.name}`,
          config: {
            xAxis: [col1.name],
            yAxis: [col2.name]
          },
          priority: calculateChartPriority('scatter', { numericCount: numericColumns.length }),
          reasoning: `Two numeric variables (${col1.name}, ${col2.name}) can reveal correlations`
        });
      }
    }
  }

  // 5. Histogram Suggestions (for distribution analysis)
  numericColumns.forEach(numCol => {
    const values = data.map(row => row[numCol.name]).filter(val => !isNaN(val) && val !== null);
    const range = Math.max(...values) - Math.min(...values);
    
    if (values.length > 10 && range > 0) {
      suggestions.push({
        type: 'histogram',
        title: `${numCol.name} Distribution`,
        description: `Show frequency distribution of ${numCol.name} values`,
        config: {
          valueField: numCol.name,
          bins: Math.min(20, Math.ceil(Math.sqrt(values.length)))
        },
        priority: calculateChartPriority('histogram', { dataSize: values.length, range }),
        reasoning: `${values.length} data points in ${numCol.name} suitable for distribution analysis`
      });
    }
  });

  // Sort suggestions by priority (highest first)
  suggestions.sort((a, b) => b.priority - a.priority);

  return {
    suggestions: suggestions.slice(0, 5), // Return top 5 suggestions
    totalSuggestions: suggestions.length,
    dataCharacteristics,
    recommendedCharts: suggestions.slice(0, 3).map(s => s.type)
  };
};

// Calculate chart priority based on data characteristics
const calculateChartPriority = (chartType, characteristics) => {
  let priority = 50; // Base priority

  switch (chartType) {
    case 'bar':
      if (characteristics.uniqueValues <= 10) priority += 30;
      if (characteristics.uniqueValues <= 5) priority += 20;
      if (characteristics.dataSize > 100) priority += 10;
      break;
      
    case 'pie':
      if (characteristics.uniqueValues <= 6) priority += 40;
      if (characteristics.uniqueValues <= 4) priority += 20;
      break;
      
    case 'line':
      if (characteristics.hasDate) priority += 50;
      if (characteristics.dataSize > 50) priority += 20;
      break;
      
    case 'scatter':
      if (characteristics.numericCount >= 3) priority += 30;
      if (characteristics.numericCount >= 5) priority += 20;
      break;
      
    case 'histogram':
      if (characteristics.dataSize > 50) priority += 20;
      if (characteristics.range > 10) priority += 15;
      break;
  }

  return Math.min(priority, 100); // Cap at 100
};

// Analyze data characteristics for better suggestions
const analyzeDataCharacteristics = (data, columns) => {
  const characteristics = {
    rowCount: data.length,
    columnCount: columns.length,
    numericColumns: columns.filter(col => col.type === 'number').length,
    textColumns: columns.filter(col => col.type === 'string').length,
    dateColumns: columns.filter(col => col.type === 'date').length,
    dataComplexity: 'simple'
  };

  // Determine data complexity
  if (characteristics.columnCount > 10 || characteristics.rowCount > 1000) {
    characteristics.dataComplexity = 'complex';
  } else if (characteristics.columnCount > 5 || characteristics.rowCount > 100) {
    characteristics.dataComplexity = 'medium';
  }

  // Analyze categorical data distribution
  const textColumns = columns.filter(col => col.type === 'string');
  characteristics.categoricalAnalysis = textColumns.map(col => {
    const uniqueValues = new Set(data.map(row => row[col.name])).size;
    return {
      column: col.name,
      uniqueValues,
      suitableForVisualization: uniqueValues <= 20 && uniqueValues >= 2
    };
  });

  return characteristics;
};

// Generate automatic charts based on top suggestions
const generateAutoCharts = async (excelData, maxCharts = 3) => {
  const { suggestions } = suggestCharts(excelData);
  const autoCharts = [];

  // Take top suggestions up to maxCharts
  const topSuggestions = suggestions.slice(0, maxCharts);

  for (const suggestion of topSuggestions) {
    autoCharts.push({
      title: suggestion.title,
      description: suggestion.description,
      type: suggestion.type,
      config: suggestion.config,
      autoGenerated: true,
      priority: suggestion.priority,
      reasoning: suggestion.reasoning
    });
  }

  return autoCharts;
};

module.exports = {
  suggestCharts,
  generateAutoCharts,
  calculateChartPriority,
  analyzeDataCharacteristics
};
