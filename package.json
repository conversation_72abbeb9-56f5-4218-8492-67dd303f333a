{"name": "excel-analytics-platform", "version": "1.0.0", "description": "A platform for analyzing Excel data", "scripts": {"start": "node backend/server.js", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "vercel-build": "echo 'Root build step'", "setup-git": "git init && git add . && git commit -m \"Initial commit\"", "deploy": "vercel --prod"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@tailwindcss/postcss": "^4.1.10", "concurrently": "^8.2.2"}, "dependencies": {"dotenv": "^16.5.0", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "mongodb": "4.0", "mongoose": "^8.16.0"}, "engines": {"node": ">=14.0.0"}}