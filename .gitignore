# Dependencies
node_modules/
/.pnp
.pnp.js

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build files
/frontend/build

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Testing
/coverage

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
.vercel
f r o n t e n d - b a c k u p / 
 
 