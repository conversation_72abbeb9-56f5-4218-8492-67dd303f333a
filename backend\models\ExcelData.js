const mongoose = require('mongoose');

const excelDataSchema = new mongoose.Schema({
  filename: {
    type: String,
    required: [true, 'Filename is required'],
    trim: true
  },
  originalName: {
    type: String,
    required: [true, 'Original filename is required'],
    trim: true
  },
  fileSize: {
    type: Number,
    required: [true, 'File size is required']
  },
  mimeType: {
    type: String,
    required: [true, 'MIME type is required']
  },
  uploadPath: {
    type: String,
    required: [true, 'Upload path is required']
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  sheetNames: [{
    type: String,
    trim: true
  }],
  totalRows: {
    type: Number,
    default: 0
  },
  totalColumns: {
    type: Number,
    default: 0
  },
  columns: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: ['string', 'number', 'date', 'boolean'],
      default: 'string'
    },
    sampleValues: [mongoose.Schema.Types.Mixed]
  }],
  data: [{
    type: mongoose.Schema.Types.Mixed
  }],
  processedData: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  metadata: {
    hasHeaders: {
      type: Boolean,
      default: true
    },
    encoding: {
      type: String,
      default: 'utf8'
    },
    delimiter: {
      type: String,
      default: ','
    },
    dateFormat: {
      type: String,
      default: 'MM/DD/YYYY'
    }
  },
  status: {
    type: String,
    enum: ['uploading', 'processing', 'completed', 'error'],
    default: 'uploading'
  },
  errorMessage: {
    type: String,
    default: ''
  },
  tags: [{
    type: String,
    trim: true
  }],
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  downloadCount: {
    type: Number,
    default: 0
  },
  lastAccessed: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for better query performance
excelDataSchema.index({ user: 1, createdAt: -1 });
excelDataSchema.index({ status: 1 });
excelDataSchema.index({ isPublic: 1 });
excelDataSchema.index({ tags: 1 });

// Pre-save middleware to update updatedAt
excelDataSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance method to get summary
excelDataSchema.methods.getSummary = function() {
  return {
    _id: this._id,
    filename: this.filename,
    originalName: this.originalName,
    fileSize: this.fileSize,
    totalRows: this.totalRows,
    totalColumns: this.totalColumns,
    status: this.status,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt
  };
};

// Static method to find by user
excelDataSchema.statics.findByUser = function(userId) {
  return this.find({ user: userId }).sort({ createdAt: -1 });
};

// Static method to find public files
excelDataSchema.statics.findPublic = function() {
  return this.find({ isPublic: true, status: 'completed' }).sort({ createdAt: -1 });
};

module.exports = mongoose.model('ExcelData', excelDataSchema);
