// Mock data service for local development
export const mockData = {
  // Dashboard stats
  dashboardStats: {
    totalUploads: 24,
    processedFiles: 18,
    pendingFiles: 4,
    errorFiles: 2,
    totalCharts: 12,
    activeUsers: 1,
    storageUsed: '2.4 GB',
    apiRequests: 156
  },

  // Recent uploads
  recentUploads: [
    {
      id: 'file_001',
      _id: 'file_001',
      filename: 'sales_data_2024.xlsx',
      originalName: 'sales_data_2024.xlsx',
      size: 1024000,
      status: 'processed',
      uploadedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      processedAt: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(),
      sheets: ['Q1_Sales', 'Q2_Sales', 'Summary'],
      rowCount: 1250
    },
    {
      id: 'file_002',
      _id: 'file_002',
      filename: 'inventory_report.xlsx',
      originalName: 'inventory_report.xlsx',
      size: 756000,
      status: 'processed',
      uploadedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      processedAt: new Date(Date.now() - 3.5 * 60 * 60 * 1000).toISOString(),
      sheets: ['Current_Stock', 'Reorder_List'],
      rowCount: 890
    },
    {
      id: 'file_003',
      _id: 'file_003',
      filename: 'customer_analysis.xlsx',
      originalName: 'customer_analysis.xlsx',
      size: 2048000,
      status: 'pending',
      uploadedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      sheets: ['Customer_Data'],
      rowCount: 2100
    }
  ],

  // Recent charts
  recentCharts: [
    {
      id: 'chart_001',
      _id: 'chart_001',
      title: 'Q1 Sales Performance',
      type: 'bar',
      fileId: 'file_001',
      fileName: 'sales_data_2024.xlsx',
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      config: {
        xAxis: ['Month'],
        yAxis: ['Sales_Amount'],
        chartType: 'bar'
      }
    },
    {
      id: 'chart_002',
      _id: 'chart_002',
      title: 'Inventory Distribution',
      type: 'pie',
      fileId: 'file_002',
      fileName: 'inventory_report.xlsx',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      config: {
        labelColumn: 'Category',
        valueColumn: 'Stock_Count',
        chartType: 'pie'
      }
    }
  ],

  // All files
  allFiles: [
    {
      id: 'file_001',
      _id: 'file_001',
      filename: 'sales_data_2024.xlsx',
      originalName: 'sales_data_2024.xlsx',
      size: 1024000,
      status: 'processed',
      uploadedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      processedAt: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString(),
      sheets: ['Q1_Sales', 'Q2_Sales', 'Summary'],
      rowCount: 1250,
      excelData: {
        sheetName: 'Q1_Sales',
        columns: ['Month', 'Sales_Amount', 'Units_Sold', 'Region'],
        data: [
          { Month: 'January', Sales_Amount: 45000, Units_Sold: 150, Region: 'North' },
          { Month: 'February', Sales_Amount: 52000, Units_Sold: 175, Region: 'North' },
          { Month: 'March', Sales_Amount: 48000, Units_Sold: 160, Region: 'North' }
        ]
      }
    },
    {
      id: 'file_002',
      _id: 'file_002',
      filename: 'inventory_report.xlsx',
      originalName: 'inventory_report.xlsx',
      size: 756000,
      status: 'processed',
      uploadedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      processedAt: new Date(Date.now() - 3.5 * 60 * 60 * 1000).toISOString(),
      sheets: ['Current_Stock', 'Reorder_List'],
      rowCount: 890,
      excelData: {
        sheetName: 'Current_Stock',
        columns: ['Category', 'Stock_Count', 'Unit_Price', 'Total_Value'],
        data: [
          { Category: 'Electronics', Stock_Count: 245, Unit_Price: 299.99, Total_Value: 73497.55 },
          { Category: 'Clothing', Stock_Count: 180, Unit_Price: 49.99, Total_Value: 8998.20 },
          { Category: 'Books', Stock_Count: 320, Unit_Price: 19.99, Total_Value: 6396.80 }
        ]
      }
    },
    {
      id: 'file_003',
      _id: 'file_003',
      filename: 'customer_analysis.xlsx',
      originalName: 'customer_analysis.xlsx',
      size: 2048000,
      status: 'pending',
      uploadedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      sheets: ['Customer_Data'],
      rowCount: 2100
    }
  ],

  // All charts
  allCharts: [
    {
      id: 'chart_001',
      _id: 'chart_001',
      title: 'Q1 Sales Performance',
      type: 'bar',
      fileId: 'file_001',
      fileName: 'sales_data_2024.xlsx',
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      config: {
        xAxis: ['Month'],
        yAxis: ['Sales_Amount'],
        chartType: 'bar'
      },
      chartData: {
        labels: ['January', 'February', 'March'],
        datasets: [{
          label: 'Sales Amount',
          data: [45000, 52000, 48000],
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      }
    },
    {
      id: 'chart_002',
      _id: 'chart_002',
      title: 'Inventory Distribution',
      type: 'pie',
      fileId: 'file_002',
      fileName: 'inventory_report.xlsx',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      config: {
        labelColumn: 'Category',
        valueColumn: 'Stock_Count',
        chartType: 'pie'
      },
      chartData: {
        labels: ['Electronics', 'Clothing', 'Books'],
        datasets: [{
          data: [245, 180, 320],
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 205, 86, 0.6)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 205, 86, 1)'
          ],
          borderWidth: 1
        }]
      }
    }
  ]
};

// Mock API functions
export const mockAPI = {
  // Dashboard data
  getDashboardData: () => {
    return Promise.resolve({
      data: {
        stats: mockData.dashboardStats,
        recentUploads: mockData.recentUploads.slice(0, 5),
        recentCharts: mockData.recentCharts.slice(0, 5)
      }
    });
  },

  // Files
  getFiles: (page = 1, limit = 10) => {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const files = mockData.allFiles.slice(startIndex, endIndex);
    
    return Promise.resolve({
      data: {
        files,
        totalFiles: mockData.allFiles.length,
        totalPages: Math.ceil(mockData.allFiles.length / limit),
        currentPage: page
      }
    });
  },

  getFileById: (id) => {
    const file = mockData.allFiles.find(f => f.id === id || f._id === id);
    if (file) {
      return Promise.resolve({ data: file });
    } else {
      return Promise.reject({ response: { status: 404, data: { message: 'File not found' } } });
    }
  },

  // Charts
  getCharts: () => {
    return Promise.resolve({
      data: mockData.allCharts
    });
  },

  getChartById: (id) => {
    const chart = mockData.allCharts.find(c => c.id === id || c._id === id);
    if (chart) {
      return Promise.resolve({ data: chart });
    } else {
      return Promise.reject({ response: { status: 404, data: { message: 'Chart not found' } } });
    }
  }
};
