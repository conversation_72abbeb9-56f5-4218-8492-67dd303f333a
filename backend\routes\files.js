const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { body, validationResult } = require('express-validator');
const ExcelData = require('../models/ExcelData');
// Authentication middleware removed - no login required
const { processExcelFile, validateExcelFile, generateUniqueFilename } = require('../utils/excelProcessor');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = process.env.UPLOAD_PATH || './uploads';
    
    // Create uploads directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueName = generateUniqueFilename(file.originalname);
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    try {
      validateExcelFile(file);
      cb(null, true);
    } catch (error) {
      cb(new Error(error.message), false);
    }
  }
});

// @desc    Upload Excel file
// @route   POST /api/files/upload
// @access  Private
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const { description, tags } = req.body;

    // Create initial database record
    const excelData = new ExcelData({
      filename: req.file.filename,
      originalName: req.file.originalname,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      uploadPath: req.file.path,
      user: 'default-user', // No authentication required
      status: 'processing',
      description: description || '',
      tags: tags ? tags.split(',').map(tag => tag.trim()) : []
    });

    await excelData.save();

    // Process the Excel file asynchronously
    try {
      const processedData = await processExcelFile(req.file.path);
      
      // Update the database record with processed data
      excelData.sheetNames = processedData.sheetNames;
      excelData.totalRows = processedData.totalRows;
      excelData.totalColumns = processedData.totalColumns;
      excelData.columns = processedData.columns;
      excelData.data = processedData.data;
      excelData.processedData = processedData.processedData;
      excelData.metadata = processedData.metadata;
      excelData.status = 'completed';
      
      await excelData.save();

      res.status(201).json({
        success: true,
        message: 'File uploaded and processed successfully',
        data: excelData.getSummary()
      });

    } catch (processingError) {
      console.error('File processing error:', processingError);
      
      // Update status to error
      excelData.status = 'error';
      excelData.errorMessage = processingError.message;
      await excelData.save();

      res.status(400).json({
        success: false,
        message: 'File uploaded but processing failed',
        error: processingError.message
      });
    }

  } catch (error) {
    console.error('Upload error:', error);
    
    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: 'File upload failed',
      error: error.message
    });
  }
});

// @desc    Get all files for user
// @route   GET /api/files
// @access  Private
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;

    const query = {}; // Get all files regardless of user
    
    // Filter by status if provided
    if (status) {
      query.status = status;
    }
    
    // Search in filename or original name
    if (search) {
      query.$or = [
        { filename: { $regex: search, $options: 'i' } },
        { originalName: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const files = await ExcelData.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select('-data -processedData'); // Exclude large data fields

    const total = await ExcelData.countDocuments(query);

    res.json(files);

  } catch (error) {
    console.error('Get files error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve files'
    });
  }
});

// @desc    Get recent files
// @route   GET /api/files/recent
// @access  Private
router.get('/recent', async (req, res) => {
  try {
    const { limit = 5 } = req.query;

    const files = await ExcelData.find({})
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .select('-data -processedData'); // Exclude large data fields

    res.json(files);

  } catch (error) {
    console.error('Get recent files error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve recent files'
    });
  }
});

// @desc    Get single file
// @route   GET /api/files/:id
// @access  Private
router.get('/:id', async (req, res) => {
  try {
    const excelData = await ExcelData.findById(req.params.id);

    // Update last accessed time
    excelData.lastAccessed = Date.now();
    await excelData.save();

    res.json({
      success: true,
      data: excelData
    });

  } catch (error) {
    console.error('Get file error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve file'
    });
  }
});

// @desc    Get file data (for charts and analysis)
// @route   GET /api/files/:id/data
// @access  Private
router.get('/:id/data', async (req, res) => {
  try {
    const excelData = await ExcelData.findById(req.params.id);

    if (excelData.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: 'File is not yet processed or has errors'
      });
    }

    // Update last accessed time
    excelData.lastAccessed = Date.now();
    await excelData.save();

    res.json({
      success: true,
      data: {
        sheets: excelData.data || [],
        columns: excelData.columns || [],
        metadata: excelData.metadata || {},
        processedData: excelData.processedData || {}
      }
    });

  } catch (error) {
    console.error('Get file data error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve file data'
    });
  }
});

// @desc    Update file metadata
// @route   PUT /api/files/:id
// @access  Private
router.put('/:id', [
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot be more than 500 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const excelData = await ExcelData.findById(req.params.id);
    const { description, tags, isPublic } = req.body;

    if (description !== undefined) {
      excelData.description = description;
    }

    if (tags !== undefined) {
      excelData.tags = tags;
    }

    if (isPublic !== undefined) {
      excelData.isPublic = isPublic;
    }

    await excelData.save();

    res.json({
      success: true,
      message: 'File updated successfully',
      data: excelData.getSummary()
    });

  } catch (error) {
    console.error('Update file error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update file'
    });
  }
});

// @desc    Delete file
// @route   DELETE /api/files/:id
// @access  Private
router.delete('/:id', async (req, res) => {
  try {
    const excelData = await ExcelData.findById(req.params.id);

    // Delete physical file
    if (fs.existsSync(excelData.uploadPath)) {
      fs.unlinkSync(excelData.uploadPath);
    }

    // Delete from database
    await ExcelData.findByIdAndDelete(excelData._id);

    res.json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete file'
    });
  }
});

module.exports = router;
