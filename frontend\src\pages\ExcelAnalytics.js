import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  Chip,
  useTheme,
  useMediaQuery,
  Fade,
  Collapse
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Analytics as AnalyticsIcon,
  BarChart as BarChartIcon,
  Lightbulb as LightbulbIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  AutoGraph as AutoGraphIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import api from '../utils/api';
import AuthContext from '../context/AuthContext';
import FileUpload from '../components/FileUpload';
import AIInsightPanel from '../components/AIInsightPanel';
import { renderChart } from '../utils/chartUtils';

// Import Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import { Bar, Line, Pie, Doughnut } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

const ExcelAnalytics = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [processedData, setProcessedData] = useState(null);
  const [generatedCharts, setGeneratedCharts] = useState([]);
  const [aiInsights, setAiInsights] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { token } = useContext(AuthContext);
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isDarkMode = theme.palette.mode === 'dark';

  const steps = [
    {
      label: 'Upload Excel File',
      description: 'Upload your Excel file for analysis',
      icon: <CloudUploadIcon />
    },
    {
      label: 'AI Processing',
      description: 'AI analyzes your data and detects patterns',
      icon: <AnalyticsIcon />
    },
    {
      label: 'Chart Generation',
      description: 'Automatic chart creation based on your data',
      icon: <BarChartIcon />
    },
    {
      label: 'AI Insights',
      description: 'Get intelligent insights and recommendations',
      icon: <LightbulbIcon />
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 300, damping: 24 }
    }
  };

  // Handle successful file upload
  const handleUploadSuccess = async (uploadResponse) => {
    try {
      setUploadedFile(uploadResponse.data);
      setActiveStep(1);
      setLoading(true);
      
      // Process the uploaded file data
      await processUploadedData(uploadResponse.data);
      
    } catch (error) {
      console.error('Error handling upload success:', error);
      setError('Failed to process uploaded file');
      setLoading(false);
    }
  };

  // Process uploaded data and generate charts
  const processUploadedData = async (fileData) => {
    try {
      setActiveStep(1);
      
      // Simulate AI processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Get the processed Excel data
      const response = await api.get(`/api/files/${fileData._id}`);
      const processedFileData = response.data;
      setProcessedData(processedFileData);
      
      setActiveStep(2);
      
      // Auto-generate charts based on data
      await generateAutoCharts(processedFileData);
      
    } catch (error) {
      console.error('Error processing data:', error);
      setError('Failed to process Excel data');
      setLoading(false);
    }
  };

  // Auto-generate charts based on intelligent data analysis
  const generateAutoCharts = async (fileData) => {
    try {
      const charts = [];

      // Get intelligent chart suggestions from backend
      const suggestionsResponse = await api.post('/api/charts/suggestions', {
        excelDataId: fileData._id
      });

      const { suggestions } = suggestionsResponse.data;

      // Generate charts based on top 3 suggestions
      const topSuggestions = suggestions.slice(0, 3);

      for (const suggestion of topSuggestions) {
        try {
          const chartResponse = await api.post('/api/charts', {
            title: suggestion.title,
            description: suggestion.description,
            type: suggestion.type,
            excelDataId: fileData._id,
            config: suggestion.config,
            autoGenerated: true
          });

          charts.push({
            ...chartResponse.data,
            reasoning: suggestion.reasoning,
            priority: suggestion.priority
          });
        } catch (chartError) {
          console.warn(`Failed to create ${suggestion.type} chart:`, chartError);
          // Continue with other charts even if one fails
        }
      }

      setGeneratedCharts(charts);
      setActiveStep(3);

      // Generate AI insights
      await generateAIInsights(fileData, charts);

    } catch (error) {
      console.error('Error generating charts:', error);
      // Fallback to basic chart generation
      await generateBasicCharts(fileData);
    }
  };

  // Fallback basic chart generation
  const generateBasicCharts = async (fileData) => {
    try {
      const charts = [];
      const numericColumns = fileData.columns?.filter(col => col.type === 'number') || [];
      const textColumns = fileData.columns?.filter(col => col.type === 'string') || [];

      if (numericColumns.length > 0 && textColumns.length > 0) {
        const barChartResponse = await api.post('/api/charts', {
          title: `${textColumns[0].name} vs ${numericColumns[0].name}`,
          type: 'bar',
          config: {
            xAxis: [textColumns[0].name],
            yAxis: [numericColumns[0].name],
            aggregationMethod: 'sum'
          },
          excelDataId: fileData._id,
          description: `Basic bar chart showing ${textColumns[0].name} vs ${numericColumns[0].name}`
        });

        charts.push(barChartResponse.data);
      }

      setGeneratedCharts(charts);
      setActiveStep(3);
      await generateAIInsights(fileData, charts);

    } catch (error) {
      console.error('Error in fallback chart generation:', error);
      setError('Failed to generate charts');
      setLoading(false);
    }
  };

  // Generate AI insights for the data
  const generateAIInsights = async (fileData, charts) => {
    try {
      // Generate insights for the first chart if available
      if (charts.length > 0) {
        const insightResponse = await api.post('/api/ai-insights', {
          excelDataId: fileData._id,
          chartId: charts[0]._id,
          insightType: 'summary'
        });
        
        setAiInsights(insightResponse.data);
      }
      
      setActiveStep(4);
      setLoading(false);
      
    } catch (error) {
      console.error('Error generating AI insights:', error);
      setError('Failed to generate AI insights');
      setLoading(false);
    }
  };

  // Reset the workflow
  const handleReset = () => {
    setActiveStep(0);
    setUploadedFile(null);
    setProcessedData(null);
    setGeneratedCharts([]);
    setAiInsights(null);
    setLoading(false);
    setError(null);
  };

  // Render chart component
  const renderChartComponent = (chart) => {
    if (!chart.processedData) return null;
    
    const chartProps = {
      data: chart.processedData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: chart.title
          },
          legend: {
            position: 'top'
          }
        }
      }
    };

    switch (chart.type) {
      case 'bar':
        return <Bar {...chartProps} />;
      case 'line':
        return <Line {...chartProps} />;
      case 'pie':
        return <Pie {...chartProps} />;
      case 'doughnut':
        return <Doughnut {...chartProps} />;
      default:
        return <Bar {...chartProps} />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header */}
        <motion.div variants={itemVariants}>
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
              Excel Analytics Platform
            </Typography>
            <Typography variant="h6" color="textSecondary" sx={{ mb: 2 }}>
              Upload your Excel file and get instant AI-powered insights and visualizations
            </Typography>
          </Box>
        </motion.div>

        {/* Error Alert */}
        {error && (
          <motion.div variants={itemVariants}>
            <Alert 
              severity="error" 
              sx={{ mb: 3 }}
              action={
                <Button color="inherit" size="small" onClick={handleReset}>
                  Reset
                </Button>
              }
            >
              {error}
            </Alert>
          </motion.div>
        )}

        {/* Main Workflow Stepper */}
        <motion.div variants={itemVariants}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Stepper activeStep={activeStep} orientation={isMobile ? 'vertical' : 'horizontal'}>
              {steps.map((step, index) => (
                <Step key={step.label}>
                  <StepLabel
                    icon={
                      activeStep > index ? (
                        <CheckCircleIcon color="success" />
                      ) : activeStep === index && loading ? (
                        <CircularProgress size={24} />
                      ) : (
                        step.icon
                      )
                    }
                  >
                    {step.label}
                  </StepLabel>
                  {isMobile && (
                    <StepContent>
                      <Typography variant="body2" color="textSecondary">
                        {step.description}
                      </Typography>
                    </StepContent>
                  )}
                </Step>
              ))}
            </Stepper>
          </Paper>
        </motion.div>

        {/* Step Content */}
        <motion.div variants={itemVariants}>
          {/* Step 1: File Upload */}
          {activeStep === 0 && (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <CloudUploadIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
              <Typography variant="h5" gutterBottom>
                Upload Your Excel File
              </Typography>
              <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
                Select an Excel file (.xlsx or .xls) to begin the analysis
              </Typography>
              <FileUpload onUploadSuccess={handleUploadSuccess} />
            </Paper>
          )}

          {/* Step 2: AI Processing */}
          {activeStep === 1 && (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <CircularProgress size={64} sx={{ mb: 2 }} />
              <Typography variant="h5" gutterBottom>
                AI is Analyzing Your Data
              </Typography>
              <Typography variant="body1" color="textSecondary">
                Our AI is processing your Excel file and detecting patterns, anomalies, and insights...
              </Typography>
              {uploadedFile && (
                <Box sx={{ mt: 2 }}>
                  <Chip
                    label={`Processing: ${uploadedFile.originalName}`}
                    color="primary"
                    variant="outlined"
                  />
                </Box>
              )}
            </Paper>
          )}

          {/* Step 3: Chart Generation */}
          {activeStep === 2 && (
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <CircularProgress size={64} sx={{ mb: 2 }} />
              <Typography variant="h5" gutterBottom>
                Generating Charts
              </Typography>
              <Typography variant="body1" color="textSecondary">
                Creating visualizations based on your data analysis...
              </Typography>
            </Paper>
          )}

          {/* Step 4: Results Display */}
          {activeStep >= 3 && (
            <Grid container spacing={3}>
              {/* Generated Charts */}
              {generatedCharts.length > 0 && (
                <Grid item xs={12}>
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <BarChartIcon sx={{ mr: 1 }} />
                      Generated Charts
                    </Typography>
                    <Divider sx={{ mb: 3 }} />
                    <Grid container spacing={3}>
                      {generatedCharts.map((chart, index) => (
                        <Grid item xs={12} md={6} key={chart._id}>
                          <Card>
                            <CardContent>
                              <Typography variant="h6" gutterBottom>
                                {chart.title}
                              </Typography>
                              <Box sx={{ height: 300, mb: 2 }}>
                                {renderChartComponent(chart)}
                              </Box>
                            </CardContent>
                            <CardActions>
                              <Button
                                size="small"
                                startIcon={<VisibilityIcon />}
                                onClick={() => navigate(`/charts/${chart._id}`)}
                              >
                                View Details
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  </Paper>
                </Grid>
              )}

              {/* AI Insights */}
              {generatedCharts.length > 0 && (
                <Grid item xs={12}>
                  <AIInsightPanel chartId={generatedCharts[0]._id} token={token} />
                </Grid>
              )}

              {/* Data Summary */}
              {processedData && (
                <Grid item xs={12}>
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <AutoGraphIcon sx={{ mr: 1 }} />
                      Data Summary
                    </Typography>
                    <Divider sx={{ mb: 3 }} />
                    <Grid container spacing={2}>
                      <Grid item xs={6} sm={3}>
                        <Card variant="outlined">
                          <CardContent sx={{ textAlign: 'center' }}>
                            <Typography variant="h4" color="primary">
                              {processedData.totalRows}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Total Rows
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Card variant="outlined">
                          <CardContent sx={{ textAlign: 'center' }}>
                            <Typography variant="h4" color="primary">
                              {processedData.totalColumns}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Total Columns
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Card variant="outlined">
                          <CardContent sx={{ textAlign: 'center' }}>
                            <Typography variant="h4" color="primary">
                              {processedData.columns?.filter(col => col.type === 'number').length || 0}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Numeric Columns
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                      <Grid item xs={6} sm={3}>
                        <Card variant="outlined">
                          <CardContent sx={{ textAlign: 'center' }}>
                            <Typography variant="h4" color="primary">
                              {generatedCharts.length}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              Generated Charts
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              )}

              {/* Action Buttons */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={handleReset}
                    startIcon={<RefreshIcon />}
                  >
                    Analyze Another File
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/charts')}
                    startIcon={<BarChartIcon />}
                  >
                    View All Charts
                  </Button>
                </Box>
              </Grid>
            </Grid>
          )}
        </motion.div>
      </motion.div>
    </Container>
  );
};

export default ExcelAnalytics;
